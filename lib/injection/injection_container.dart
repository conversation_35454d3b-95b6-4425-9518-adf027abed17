import 'package:get_it/get_it.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Data Sources
import '../data/datasources/firebase_auth_datasource.dart';
import '../data/datasources/firebase_resume_datasource.dart';
import '../data/datasources/local_resume_datasource.dart';
import '../data/datasources/pdf_export_datasource.dart';
import '../data/datasources/local_activity_datasource.dart';

// Repositories
import '../data/repositories/auth_repository_impl.dart';
import '../data/repositories/resume_repository_impl.dart';
import '../data/repositories/activity_repository_impl.dart';
import '../domain/repositories/auth_repository.dart';
import '../domain/repositories/resume_repository.dart';
import '../domain/repositories/activity_repository.dart';

// Use Cases
import '../domain/usecases/auth_usecases.dart';
import '../domain/usecases/resume_usecases.dart';
import '../domain/usecases/activity_usecases.dart';

// Cubits
import '../presentation/cubits/theme/theme_cubit.dart';
import '../presentation/cubits/auth/auth_cubit.dart';
import '../presentation/cubits/resume/resume_cubit.dart';
import '../presentation/cubits/activity/activity_cubit.dart';
import '../presentation/cubits/template/template_cubit.dart';

final sl = GetIt.instance;

Future<void> init() async {
  // External dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerLazySingleton(() => sharedPreferences);
  
  sl.registerLazySingleton(() => FirebaseAuth.instance);
  sl.registerLazySingleton(() => FirebaseFirestore.instance);
  sl.registerLazySingleton(() => GoogleSignIn());

  // Data sources
  sl.registerLazySingleton<FirebaseAuthDataSource>(
    () => FirebaseAuthDataSource(
      sl<FirebaseAuth>(),
      sl<GoogleSignIn>(),
      sl<FirebaseFirestore>(),
    ),
  );

  sl.registerLazySingleton<FirebaseResumeDataSource>(
    () => FirebaseResumeDataSource(
      sl<FirebaseFirestore>(),
      sl<FirebaseAuth>(),
    ),
  );

  sl.registerLazySingleton<LocalResumeDataSource>(
    () => LocalResumeDataSource(),
  );

  sl.registerLazySingleton<PdfExportDataSource>(
    () => PdfExportDataSource(),
  );

  sl.registerLazySingleton<LocalActivityDataSource>(
    () => LocalActivityDataSource(),
  );

  // Repositories
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(sl<FirebaseAuthDataSource>()),
  );

  sl.registerLazySingleton<ResumeRepository>(
    () => ResumeRepositoryImpl(
      sl<FirebaseResumeDataSource>(),
      sl<LocalResumeDataSource>(),
      sl<PdfExportDataSource>(),
    ),
  );

  sl.registerLazySingleton<ActivityRepository>(
    () => ActivityRepositoryImpl(sl<LocalActivityDataSource>()),
  );

  // Use cases
  sl.registerLazySingleton(() => AuthUseCases(sl<AuthRepository>()));
  sl.registerLazySingleton(() => ResumeUseCases(sl<ResumeRepository>()));
  sl.registerLazySingleton(() => ActivityUseCases(sl<ActivityRepository>()));

  // Cubits
  sl.registerFactory(() => ThemeCubit(sl<SharedPreferences>()));
  sl.registerFactory(() => AuthCubit(sl<AuthUseCases>()));
  sl.registerFactory(() => ResumeCubit(sl<ResumeUseCases>(), sl<ActivityUseCases>()));
  sl.registerFactory(() => ActivityCubit(sl<ActivityUseCases>()));
  sl.registerFactory(() => TemplateCubit());
}
