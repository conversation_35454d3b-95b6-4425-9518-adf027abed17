import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

enum TemplateCategory {
  professional,
  creative,
  modern,
  minimal,
}

class ResumeTemplateModel extends Equatable {
  final String id;
  final String name;
  final String description;
  final IconData previewIcon;
  final TemplateCategory category;
  final bool isPremium;
  final TemplateStyle style;

  const ResumeTemplateModel({
    required this.id,
    required this.name,
    required this.description,
    required this.previewIcon,
    required this.category,
    required this.isPremium,
    required this.style,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        previewIcon,
        category,
        isPremium,
        style,
      ];
}

class TemplateStyle extends Equatable {
  final Color primaryColor;
  final Color secondaryColor;
  final Color accentColor;
  final String fontFamily;
  final double headerFontSize;
  final double bodyFontSize;
  final double sectionSpacing;
  final bool showProfileImage;
  final bool showSectionIcons;
  final TemplateLayout layout;

  const TemplateStyle({
    required this.primaryColor,
    required this.secondaryColor,
    required this.accentColor,
    required this.fontFamily,
    required this.headerFontSize,
    required this.bodyFontSize,
    required this.sectionSpacing,
    required this.showProfileImage,
    required this.showSectionIcons,
    required this.layout,
  });

  @override
  List<Object?> get props => [
        primaryColor,
        secondaryColor,
        accentColor,
        fontFamily,
        headerFontSize,
        bodyFontSize,
        sectionSpacing,
        showProfileImage,
        showSectionIcons,
        layout,
      ];
}

enum TemplateLayout {
  singleColumn,
  twoColumn,
  sidebar,
  modern,
}

// Template data repository
class TemplateRepository {
  static const String defaultTemplateId = 'classic_professional';

  static List<ResumeTemplateModel> getAllTemplates() {
    return [
      // Professional Templates
      ResumeTemplateModel(
        id: 'classic_professional',
        name: 'Classic Professional',
        description: 'Clean and professional design perfect for corporate roles',
        previewIcon: Icons.business_center,
        category: TemplateCategory.professional,
        isPremium: false,
        style: const TemplateStyle(
          primaryColor: Color(0xFF2C3E50),
          secondaryColor: Color(0xFF34495E),
          accentColor: Color(0xFF3498DB),
          fontFamily: 'Roboto',
          headerFontSize: 24.0,
          bodyFontSize: 14.0,
          sectionSpacing: 20.0,
          showProfileImage: true,
          showSectionIcons: false,
          layout: TemplateLayout.singleColumn,
        ),
      ),
      ResumeTemplateModel(
        id: 'executive_blue',
        name: 'Executive Blue',
        description: 'Sophisticated blue theme for senior positions',
        previewIcon: Icons.corporate_fare,
        category: TemplateCategory.professional,
        isPremium: true,
        style: const TemplateStyle(
          primaryColor: Color(0xFF1E3A8A),
          secondaryColor: Color(0xFF3B82F6),
          accentColor: Color(0xFF60A5FA),
          fontFamily: 'Inter',
          headerFontSize: 26.0,
          bodyFontSize: 14.0,
          sectionSpacing: 24.0,
          showProfileImage: true,
          showSectionIcons: true,
          layout: TemplateLayout.twoColumn,
        ),
      ),
      
      // Modern Templates
      ResumeTemplateModel(
        id: 'modern_gradient',
        name: 'Modern Gradient',
        description: 'Contemporary design with gradient accents',
        previewIcon: Icons.gradient,
        category: TemplateCategory.modern,
        isPremium: false,
        style: const TemplateStyle(
          primaryColor: Color(0xFF6366F1),
          secondaryColor: Color(0xFF8B5CF6),
          accentColor: Color(0xFFA855F7),
          fontFamily: 'Inter',
          headerFontSize: 28.0,
          bodyFontSize: 15.0,
          sectionSpacing: 22.0,
          showProfileImage: true,
          showSectionIcons: true,
          layout: TemplateLayout.modern,
        ),
      ),
      ResumeTemplateModel(
        id: 'tech_stack',
        name: 'Tech Stack',
        description: 'Perfect for developers and tech professionals',
        previewIcon: Icons.code,
        category: TemplateCategory.modern,
        isPremium: true,
        style: const TemplateStyle(
          primaryColor: Color(0xFF0F172A),
          secondaryColor: Color(0xFF1E293B),
          accentColor: Color(0xFF10B981),
          fontFamily: 'JetBrains Mono',
          headerFontSize: 24.0,
          bodyFontSize: 13.0,
          sectionSpacing: 18.0,
          showProfileImage: false,
          showSectionIcons: true,
          layout: TemplateLayout.sidebar,
        ),
      ),
      
      // Creative Templates
      ResumeTemplateModel(
        id: 'creative_orange',
        name: 'Creative Orange',
        description: 'Bold and creative design for artistic roles',
        previewIcon: Icons.palette,
        category: TemplateCategory.creative,
        isPremium: false,
        style: const TemplateStyle(
          primaryColor: Color(0xFFEA580C),
          secondaryColor: Color(0xFFFB923C),
          accentColor: Color(0xFFFED7AA),
          fontFamily: 'Poppins',
          headerFontSize: 30.0,
          bodyFontSize: 14.0,
          sectionSpacing: 26.0,
          showProfileImage: true,
          showSectionIcons: true,
          layout: TemplateLayout.twoColumn,
        ),
      ),
      ResumeTemplateModel(
        id: 'artistic_purple',
        name: 'Artistic Purple',
        description: 'Elegant purple theme for creative professionals',
        previewIcon: Icons.brush,
        category: TemplateCategory.creative,
        isPremium: true,
        style: const TemplateStyle(
          primaryColor: Color(0xFF7C3AED),
          secondaryColor: Color(0xFF8B5CF6),
          accentColor: Color(0xFFC4B5FD),
          fontFamily: 'Playfair Display',
          headerFontSize: 32.0,
          bodyFontSize: 15.0,
          sectionSpacing: 28.0,
          showProfileImage: true,
          showSectionIcons: false,
          layout: TemplateLayout.modern,
        ),
      ),
      
      // Minimal Templates
      ResumeTemplateModel(
        id: 'minimal_black',
        name: 'Minimal Black',
        description: 'Clean minimal design with black accents',
        previewIcon: Icons.minimize,
        category: TemplateCategory.minimal,
        isPremium: false,
        style: const TemplateStyle(
          primaryColor: Color(0xFF000000),
          secondaryColor: Color(0xFF374151),
          accentColor: Color(0xFF6B7280),
          fontFamily: 'Source Sans Pro',
          headerFontSize: 22.0,
          bodyFontSize: 13.0,
          sectionSpacing: 16.0,
          showProfileImage: false,
          showSectionIcons: false,
          layout: TemplateLayout.singleColumn,
        ),
      ),
      ResumeTemplateModel(
        id: 'minimal_green',
        name: 'Minimal Green',
        description: 'Subtle green accents for a fresh look',
        previewIcon: Icons.eco,
        category: TemplateCategory.minimal,
        isPremium: true,
        style: const TemplateStyle(
          primaryColor: Color(0xFF059669),
          secondaryColor: Color(0xFF10B981),
          accentColor: Color(0xFF6EE7B7),
          fontFamily: 'Lato',
          headerFontSize: 24.0,
          bodyFontSize: 14.0,
          sectionSpacing: 18.0,
          showProfileImage: false,
          showSectionIcons: false,
          layout: TemplateLayout.singleColumn,
        ),
      ),
    ];
  }

  static ResumeTemplateModel getTemplateById(String id) {
    return getAllTemplates().firstWhere(
      (template) => template.id == id,
      orElse: () => getAllTemplates().first,
    );
  }

  static ResumeTemplateModel getDefaultTemplate() {
    return getTemplateById(defaultTemplateId);
  }

  static List<ResumeTemplateModel> getTemplatesByCategory(TemplateCategory category) {
    return getAllTemplates().where((template) => template.category == category).toList();
  }

  static List<ResumeTemplateModel> getFreeTemplates() {
    return getAllTemplates().where((template) => !template.isPremium).toList();
  }

  static List<ResumeTemplateModel> getPremiumTemplates() {
    return getAllTemplates().where((template) => template.isPremium).toList();
  }
}
