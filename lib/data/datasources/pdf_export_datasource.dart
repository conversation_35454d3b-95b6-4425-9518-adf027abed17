import 'dart:io';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';

import '../models/simple_resume_model.dart';

class PdfExportDataSource {
  Future<void> exportToPdf(ResumeModel resume) async {
    try {
      final pdf = pw.Document();
      
      // Add pages to PDF
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          build: (pw.Context context) {
            return [
              _buildHeader(resume.personalInfo),
              pw.SizedBox(height: 20),
              if (resume.summary.isNotEmpty) ...[
                _buildSection('Summary', resume.summary),
                pw.SizedBox(height: 20),
              ],
              if (resume.workExperience.isNotEmpty) ...[
                _buildWorkExperienceSection(resume.workExperience),
                pw.SizedBox(height: 20),
              ],
              if (resume.education.isNotEmpty) ...[
                _buildEducationSection(resume.education),
                pw.SizedBox(height: 20),
              ],
              if (resume.skills.isNotEmpty) ...[
                _buildSkillsSection(resume.skills),
                pw.SizedBox(height: 20),
              ],
              if (resume.projects.isNotEmpty) ...[
                _buildProjectsSection(resume.projects),
                pw.SizedBox(height: 20),
              ],
              if (resume.certifications.isNotEmpty) ...[
                _buildCertificationsSection(resume.certifications),
                pw.SizedBox(height: 20),
              ],
              if (resume.languages.isNotEmpty) ...[
                _buildLanguagesSection(resume.languages),
              ],
            ];
          },
        ),
      );

      // Save or print the PDF
      await _savePdf(pdf, resume.personalInfo.fullName);
    } catch (e) {
      throw Exception('Failed to export PDF: $e');
    }
  }

  pw.Widget _buildHeader(PersonalInfoModel personalInfo) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          personalInfo.fullName,
          style: pw.TextStyle(
            fontSize: 24,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 8),
        pw.Row(
          children: [
            pw.Text('${personalInfo.email} | ${personalInfo.phone}'),
          ],
        ),
        pw.Text('${personalInfo.address}, ${personalInfo.city}, ${personalInfo.state} ${personalInfo.zipCode}'),
      ],
    );
  }

  pw.Widget _buildSection(String title, String content) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 8),
        pw.Text(content),
      ],
    );
  }

  pw.Widget _buildWorkExperienceSection(List<WorkExperienceModel> experiences) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Work Experience',
          style: pw.TextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 8),
        ...experiences.map((exp) => pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 16),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    exp.jobTitle,
                    style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                  ),
                  pw.Text(_formatDateRange(exp.startDate, exp.endDate, exp.isCurrentJob)),
                ],
              ),
              pw.Text('${exp.company} - ${exp.location}'),
              pw.SizedBox(height: 4),
              pw.Text(exp.description),
              if (exp.achievements.isNotEmpty) ...[
                pw.SizedBox(height: 4),
                ...exp.achievements.map((achievement) => pw.Bullet(text: achievement)),
              ],
            ],
          ),
        )),
      ],
    );
  }

  pw.Widget _buildEducationSection(List<EducationModel> education) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Education',
          style: pw.TextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 8),
        ...education.map((edu) => pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 12),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    edu.degree,
                    style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                  ),
                  pw.Text(_formatDateRange(edu.startDate, edu.endDate, edu.isCurrentlyStudying)),
                ],
              ),
              pw.Text('${edu.institution} - ${edu.location}'),
              if (edu.gpa != null) pw.Text('GPA: ${edu.gpa}'),
              if (edu.description != null) pw.Text(edu.description!),
            ],
          ),
        )),
      ],
    );
  }

  pw.Widget _buildSkillsSection(List<SkillCategoryModel> skillCategories) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Skills',
          style: pw.TextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 8),
        ...skillCategories.map((category) => pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 8),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                category.category,
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.Text(category.skills.map((skill) => skill.name).join(', ')),
            ],
          ),
        )),
      ],
    );
  }

  pw.Widget _buildProjectsSection(List<ProjectModel> projects) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Projects',
          style: pw.TextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 8),
        ...projects.map((project) => pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 12),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                project.name,
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.Text(project.description),
              if (project.technologies.isNotEmpty)
                pw.Text('Technologies: ${project.technologies.join(', ')}'),
              if (project.projectUrl != null) pw.Text('URL: ${project.projectUrl}'),
            ],
          ),
        )),
      ],
    );
  }

  pw.Widget _buildCertificationsSection(List<CertificationModel> certifications) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Certifications',
          style: pw.TextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 8),
        ...certifications.map((cert) => pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 8),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      cert.name,
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                    ),
                    pw.Text(cert.issuer),
                  ],
                ),
              ),
              pw.Text(DateFormat('MMM yyyy').format(cert.issueDate)),
            ],
          ),
        )),
      ],
    );
  }

  pw.Widget _buildLanguagesSection(List<LanguageModel> languages) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Languages',
          style: pw.TextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 8),
        pw.Text(
          languages.map((lang) => '${lang.language} (${lang.proficiency})').join(', '),
        ),
      ],
    );
  }

  String _formatDateRange(DateTime startDate, DateTime? endDate, bool isCurrent) {
    final startFormatted = DateFormat('MMM yyyy').format(startDate);
    if (isCurrent) {
      return '$startFormatted - Present';
    } else if (endDate != null) {
      final endFormatted = DateFormat('MMM yyyy').format(endDate);
      return '$startFormatted - $endFormatted';
    } else {
      return startFormatted;
    }
  }

  Future<void> _savePdf(pw.Document pdf, String fileName) async {
    try {
      // For mobile platforms, use the printing package to save/share
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: '${fileName}_resume.pdf',
      );
    } catch (e) {
      // Fallback: save to documents directory
      final output = await getApplicationDocumentsDirectory();
      final file = File('${output.path}/${fileName}_resume.pdf');
      await file.writeAsBytes(await pdf.save());
    }
  }
}
