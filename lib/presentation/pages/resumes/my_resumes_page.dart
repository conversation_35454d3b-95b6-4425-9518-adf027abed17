import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';

import '../../cubits/auth/auth_cubit.dart';
import '../../cubits/resume/resume_cubit.dart';
import '../../../data/models/simple_resume_model.dart';
import '../../../domain/usecases/resume_usecases.dart';
import '../../widgets/common/loading_widget.dart';
import '../resume_builder/resume_builder_page.dart';

// MyResumesCubit and State classes
class MyResumesState {
  final List<ResumeModel> resumes;
  final bool isLoading;
  final String? errorMessage;

  const MyResumesState({
    this.resumes = const [],
    this.isLoading = false,
    this.errorMessage,
  });

  MyResumesState copyWith({
    List<ResumeModel>? resumes,
    bool? isLoading,
    String? errorMessage,
  }) {
    return MyResumesState(
      resumes: resumes ?? this.resumes,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
    );
  }
}

class MyResumesCubit extends Cubit<MyResumesState> {
  final ResumeUseCases _resumeUseCases;
  final Uuid _uuid = const Uuid();

  MyResumesCubit(this._resumeUseCases) : super(const MyResumesState());

  void loadUserResumes(String userId) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      final resumes = await _resumeUseCases.getUserResumes(userId);
      emit(state.copyWith(
        resumes: resumes,
        isLoading: false,
        errorMessage: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      ));
    }
  }

  void duplicateResume(ResumeModel originalResume) async {
    try {
      final duplicatedResume = originalResume.copyWith(
        id: _uuid.v4(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _resumeUseCases.saveResume(duplicatedResume);

      final updatedResumes = List<ResumeModel>.from(state.resumes)
        ..insert(0, duplicatedResume);

      emit(state.copyWith(resumes: updatedResumes));
    } catch (e) {
      emit(state.copyWith(errorMessage: e.toString()));
    }
  }

  void deleteResume(String resumeId) async {
    try {
      await _resumeUseCases.deleteResume(resumeId);

      final updatedResumes = state.resumes
          .where((resume) => resume.id != resumeId)
          .toList();

      emit(state.copyWith(resumes: updatedResumes));
    } catch (e) {
      emit(state.copyWith(errorMessage: e.toString()));
    }
  }
}

class MyResumesPage extends StatefulWidget {
  const MyResumesPage({super.key});

  @override
  State<MyResumesPage> createState() => _MyResumesPageState();
}

class _MyResumesPageState extends State<MyResumesPage> {
  late final MyResumesCubit _myResumesCubit;

  @override
  void initState() {
    super.initState();
    _myResumesCubit = MyResumesCubit(context.read<ResumeCubit>().resumeUseCases);
    _loadUserResumes();
  }

  void _loadUserResumes() {
    final user = context.read<AuthCubit>().state.user;
    if (user != null) {
      print('Loading resumes for authenticated user: ${user.id}');
      _myResumesCubit.loadUserResumes(user.id);
    } else {
      // For testing purposes, load test user data when not authenticated
      const testUserId = 'Bbb0ezu0zeQqCDHghn3LLGXceM93';
      print('Loading resumes for test user: $testUserId');
      _myResumesCubit.loadUserResumes(testUserId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Resumes'),
        actions: [
          IconButton(
            onPressed: () => _createNewResume(context),
            icon: const Icon(Icons.add),
            tooltip: 'Create New Resume',
          ),
        ],
      ),
      body: BlocBuilder<MyResumesCubit, MyResumesState>(
        bloc: _myResumesCubit,
        builder: (context, state) {
          if (state.isLoading) {
            return const LoadingWidget(message: 'Loading your resumes...');
          }

          if (state.errorMessage != null) {
            return _buildErrorState(context, state.errorMessage!);
          }

          if (state.resumes.isEmpty) {
            return _buildEmptyState(context);
          }

          return _buildResumesList(context, state.resumes);
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.description_outlined,
              size: 80,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 24),
            Text(
              'No Resumes Yet',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            Text(
              'Create your first resume to get started with your job search journey.',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => _createNewResume(context),
              icon: const Icon(Icons.add),
              label: const Text('Create Your First Resume'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 24),
            Text(
              'Error Loading Resumes',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _loadUserResumes,
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResumesList(BuildContext context, List<ResumeModel> resumes) {
    return RefreshIndicator(
      onRefresh: () async => _loadUserResumes(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: resumes.length,
        itemBuilder: (context, index) {
          final resume = resumes[index];
          return _buildResumeCard(context, resume);
        },
      ),
    );
  }

  Widget _buildResumeCard(BuildContext context, ResumeModel resume) {
    final hasContent = resume.personalInfo.firstName.isNotEmpty || 
                      resume.summary.isNotEmpty ||
                      resume.workExperience.isNotEmpty;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _openResume(context, resume),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _getResumeTitle(resume),
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _getResumeSubtitle(resume),
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleMenuAction(context, value, resume),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit),
                            SizedBox(width: 8),
                            Text('Edit'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'duplicate',
                        child: Row(
                          children: [
                            Icon(Icons.copy),
                            SizedBox(width: 8),
                            Text('Duplicate'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'export',
                        child: Row(
                          children: [
                            Icon(Icons.picture_as_pdf),
                            SizedBox(width: 8),
                            Text('Export PDF'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    hasContent ? Icons.check_circle : Icons.edit,
                    size: 16,
                    color: hasContent 
                        ? Colors.green 
                        : Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    hasContent ? 'Ready to use' : 'Draft',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: hasContent 
                          ? Colors.green 
                          : Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    'Updated ${_formatDate(resume.updatedAt)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getResumeTitle(ResumeModel resume) {
    if (resume.personalInfo.firstName.isNotEmpty) {
      return '${resume.personalInfo.firstName} ${resume.personalInfo.lastName}'.trim();
    }
    return 'Untitled Resume';
  }

  String _getResumeSubtitle(ResumeModel resume) {
    if (resume.workExperience.isNotEmpty) {
      return resume.workExperience.first.jobTitle;
    }
    if (resume.summary.isNotEmpty) {
      return resume.summary.length > 50 
          ? '${resume.summary.substring(0, 50)}...'
          : resume.summary;
    }
    return 'No content added yet';
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return DateFormat('MMM d, yyyy').format(date);
    }
  }

  void _createNewResume(BuildContext context) {
    context.read<ResumeCubit>().createNewResume();
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ResumeBuilderPage(),
      ),
    );
  }

  void _openResume(BuildContext context, ResumeModel resume) {
    // Load the full resume with subcollections from Firebase
    context.read<ResumeCubit>().loadResume(resume.id);
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ResumeBuilderPage(),
      ),
    );
  }

  void _handleMenuAction(BuildContext context, String action, ResumeModel resume) {
    switch (action) {
      case 'edit':
        _openResume(context, resume);
        break;
      case 'duplicate':
        _duplicateResume(context, resume);
        break;
      case 'export':
        _exportResume(context, resume);
        break;
      case 'delete':
        _showDeleteConfirmation(context, resume);
        break;
    }
  }

  void _duplicateResume(BuildContext context, ResumeModel resume) {
    _myResumesCubit.duplicateResume(resume);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Resume duplicated successfully!')),
    );
  }

  void _exportResume(BuildContext context, ResumeModel resume) {
    context.read<ResumeCubit>().loadResumeFromModel(resume);
    context.read<ResumeCubit>().exportToPdf();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Exporting resume to PDF...')),
    );
  }

  void _showDeleteConfirmation(BuildContext context, ResumeModel resume) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Resume'),
        content: Text('Are you sure you want to delete "${_getResumeTitle(resume)}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _myResumesCubit.deleteResume(resume.id);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Resume deleted successfully')),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _myResumesCubit.close();
    super.dispose();
  }
}
